{"version": 3, "file": "worldGenerator.js", "sourceRoot": "", "sources": ["../../src/generators/worldGenerator.ts"], "names": [], "mappings": ";;AAQA,8CA4CC;AApDD,+BAAoC;AAIpC,2CAAwD;AAIxD,SAAgB,iBAAiB,CAAC,cAA8B;IAC9D,MAAM,OAAO,GAAG,IAAA,SAAM,GAAE,CAAC;IACzB,MAAM,WAAW,GAAG,IAAI,IAAI,EAAE,CAAC;IAG/B,MAAM,YAAY,GAAsB,yBAAyB,EAAE,CAAC;IAGpE,MAAM,SAAS,GAAG,cAAc,CAAC,QAAQ,CAAC,SAAS,CAAC;IACpD,MAAM,SAAS,GAAa;QAC1B,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,SAAS,GAAG,CAAC,CAAC;QAC5B,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,SAAS,GAAG,CAAC,CAAC;KAC7B,CAAC;IACF,MAAM,aAAa,GAAW,qBAAqB,CACjD,cAAc,CAAC,MAAM,EACrB,cAAc,CAAC,IAAI,IAAI,QAAQ,EAC/B,SAAS,CACV,CAAC;IAGF,MAAM,KAAK,GAAa;QACtB,EAAE,EAAE,OAAO;QACX,MAAM,EAAE,cAAc,CAAC,MAAM;QAC7B,IAAI,EAAE,cAAc,CAAC,IAAI;QACzB,WAAW,EAAE,cAAc,CAAC,WAAW;QACvC,UAAU,EAAE,YAAY;QACxB,MAAM,EAAE,aAAa;QACrB,QAAQ,EAAE;YACR,IAAI,EAAE,cAAc,CAAC,QAAQ,CAAC,IAAI;YAClC,QAAQ,EAAE,cAAc,CAAC,QAAQ,CAAC,QAAoB;YACtD,SAAS,EAAE,cAAc,CAAC,QAAQ,CAAC,SAAS;YAC5C,UAAU,EAAE,cAAc,CAAC,QAAQ,CAAC,UAAU;YAC9C,QAAQ,EAAE,cAAc,CAAC,QAAQ,CAAC,QAAQ;YAC1C,SAAS,EAAE,cAAc,CAAC,QAAQ,CAAC,SAAS;SAC7C;QACD,QAAQ,EAAE,EAAE;QACZ,SAAS,EAAE,WAAW;QACtB,SAAS,EAAE,WAAW;KACvB,CAAC;IAGF,KAAK,CAAC,QAAQ,GAAG,iBAAiB,CAAC,KAAK,CAAC,QAAQ,EAAE,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;IAExE,OAAO,KAAK,CAAC;AACf,CAAC;AAED,SAAS,qBAAqB,CAAC,MAAc,EAAE,IAAY,EAAE,QAAkB;IAC7E,MAAM,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC;IACvB,OAAO;QACL,EAAE,EAAE,IAAA,SAAM,GAAE;QACZ,IAAI;QACJ,KAAK,EAAE,CAAC;QACR,UAAU,EAAE,CAAC;QACb,gBAAgB,EAAE,GAAG;QACrB,YAAY,EAAE,CAAC;QACf,OAAO,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;QACrD,SAAS,EAAE,GAAG;QACd,KAAK,EAAE,GAAG;QACV,SAAS,EAAE,EAAE;QACb,KAAK,EAAE,EAAE;QACT,cAAc,EAAE,CAAC;QACjB,MAAM,EAAE,CAAC;QACT,MAAM,EAAE,CAAC;QACT,OAAO,EAAE,CAAC;QACV,KAAK,EAAE,EAAE;QACT,MAAM,EAAE,EAAE;QACV,SAAS,EAAE,EAAE;QACb,QAAQ,EAAE,QAAQ;QAClB,OAAO,EAAE,EAAE;QACX,eAAe,EAAE,EAAE;QACnB,YAAY,EAAE,EAAE;QAChB,mBAAmB,EAAE,EAAE;QACvB,iBAAiB,EAAE,EAAE;QACrB,SAAS,EAAE,GAAG;QACd,UAAU,EAAE,GAAG;KAChB,CAAC;AACJ,CAAC;AAGD,SAAS,yBAAyB;IAChC,OAAO;QACL,WAAW,EAAE;YACX,GAAG,EAAE,CAAC;YACN,IAAI,EAAE,EAAE;YACR,MAAM,EAAE,CAAC;YACT,MAAM,EAAE,QAAQ;SACjB;QACD,OAAO,EAAE;YACP,WAAW,EAAE,EAAE;YACf,QAAQ,EAAE,EAAE;YACZ,SAAS,EAAE,CAAC;YACZ,aAAa,EAAE,CAAC;YAChB,UAAU,EAAE,GAAG;YACf,cAAc,EAAE,KAAK;SACtB;QACD,YAAY,EAAE,EAAE;KACjB,CAAC;AACJ,CAAC;AAGD,SAAS,iBAAiB,CAAC,QAAuB,EAAE,IAAY;IAC9D,MAAM,IAAI,GAAiC,EAAE,CAAC;IAC9C,MAAM,SAAS,GAAG,QAAQ,CAAC,SAAS,CAAC;IAGrC,MAAM,GAAG,GAAG,kBAAkB,CAAC,IAAI,CAAC,CAAC;IAGrC,MAAM,cAAc,GAAG,CAAC,CAAC;IAGzB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,EAAE,CAAC,EAAE,EAAE,CAAC;QACnC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,EAAE,CAAC,EAAE,EAAE,CAAC;YACnC,MAAM,GAAG,GAAa,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;YAC/B,MAAM,OAAO,GAAG,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC;YAC5B,MAAM,OAAO,GAAG,mBAAW,CAAC,SAAS,CAAC;YACtC,MAAM,OAAO,GAAG,KAAK,CAAC;YACtB,IAAI,MAAM,GAAG,CAAC,CAAC;YACf,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,SAAS,GAAG,CAAC,IAAI,CAAC,KAAK,SAAS,GAAG,CAAC;gBAAE,MAAM,GAAG,CAAC,CAAC;iBAC5E,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC;gBAAE,MAAM,GAAG,CAAC,CAAC;iBAClC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC;gBAAE,MAAM,GAAG,CAAC,CAAC;iBACnC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC;gBAAE,MAAM,GAAG,CAAC,CAAC;YACxC,IAAI,QAAQ,GAAG,SAAS,CAAC;YACzB,IAAI,CAAC,OAAO,CAAC,GAAG;gBACd,GAAG;gBACH,OAAO;gBACP,eAAe,EAAE,CAAC;gBAClB,OAAO;gBACP,MAAM;gBACN,QAAQ;gBACR,QAAQ,EAAE,IAAI;gBACd,YAAY,EAAE,CAAC;gBACf,OAAO,EAAE,CAAC;aACX,CAAC;QACJ,CAAC;IACH,CAAC;IAGD,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,SAAS,GAAG,CAAC,CAAC,CAAC;IAC1C,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,SAAS,GAAG,CAAC,CAAC,CAAC;IAC1C,YAAY,CAAC,IAAI,EAAE,EAAE,CAAC,EAAE,OAAO,EAAE,CAAC,EAAE,OAAO,EAAE,EAAE,CAAC,CAAC,CAAC;IAEpD,SAAS,YAAY,CAAC,IAAkC,EAAE,GAAa,EAAE,MAAc;QACrF,KAAK,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,MAAM,EAAE,CAAC,IAAI,GAAG,CAAC,CAAC,GAAG,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YACtD,KAAK,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,MAAM,EAAE,CAAC,IAAI,GAAG,CAAC,CAAC,GAAG,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;gBACtD,MAAM,EAAE,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;gBACrB,MAAM,EAAE,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;gBACrB,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,MAAM,GAAG,MAAM,EAAE,CAAC;oBACzC,MAAM,OAAO,GAAG,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC;oBAC5B,IAAI,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC;wBAClB,IAAI,CAAC,OAAO,CAAC,CAAC,QAAQ,GAAG,KAAK,CAAC;oBACjC,CAAC;gBACH,CAAC;YACH,CAAC;QACH,CAAC;IACH,CAAC;IAGC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,EAAE,CAAC,EAAE,EAAE,CAAC;QACnC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,EAAE,CAAC,EAAE,EAAE,CAAC;YACnC,MAAM,OAAO,GAAG,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC;YAC5B,IAAI,CAAC,OAAO,CAAC,CAAC,eAAe,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC7D,IAAI,IAAI,CAAC,OAAO,CAAC,CAAC,eAAe,KAAK,CAAC,EAAE,CAAC;gBACxC,CAAC,GAAG,CAAC,CAAC;gBACN,CAAC,GAAG,CAAC,CAAC;YACR,CAAC;QACH,CAAC;IACH,CAAC;IAGD,MAAM,MAAM,GAAG,SAAS,GAAG,CAAC,CAAC;IAC7B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,EAAE,CAAC,EAAE,EAAE,CAAC;QACnC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,EAAE,CAAC,EAAE,EAAE,CAAC;YACnC,MAAM,OAAO,GAAG,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC;YAC5B,MAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC;YAC9D,IAAI,OAAO,GAAkB,CAAC,CAAC;YAC/B,IAAI,IAAI,GAAG,SAAS,GAAG,GAAG;gBAAE,OAAO,GAAG,CAAC,CAAC;iBACnC,IAAI,IAAI,GAAG,SAAS,GAAG,IAAI;gBAAE,OAAO,GAAG,CAAC,CAAC;iBACzC,IAAI,IAAI,GAAG,SAAS,GAAG,IAAI;gBAAE,OAAO,GAAG,CAAC,CAAC;YAC9C,IAAI,CAAC,OAAO,CAAC,CAAC,OAAO,GAAG,OAAO,CAAC;QAClC,CAAC;IACH,CAAC;IAGD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,EAAE,CAAC,EAAE,EAAE,CAAC;QACnC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,EAAE,CAAC,EAAE,EAAE,CAAC;YACnC,MAAM,OAAO,GAAG,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC;YAC5B,MAAM,YAAY,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC,GAAG,CAAC,CAAkB,CAAC;YAC1E,IAAI,CAAC,OAAO,CAAC,CAAC,YAAY,GAAG,YAAY,CAAC;QAC5C,CAAC;IACH,CAAC;IAED,OAAO,IAAI,CAAC;AACd,CAAC;AAKD,SAAS,kBAAkB,CAAC,IAAY;IACtC,IAAI,IAAI,GAAG,CAAC,CAAC;IACb,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;QACrC,IAAI,GAAG,CAAC,IAAI,IAAI,CAAC,CAAC,GAAG,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;QAC/C,IAAI,IAAI,CAAC,CAAC;IACZ,CAAC;IAGD,OAAO;QACL,IAAI,GAAG,CAAC,IAAI,GAAG,IAAI,GAAG,KAAK,CAAC,GAAG,MAAM,CAAC;QACtC,OAAO,IAAI,GAAG,MAAM,CAAC;IACvB,CAAC,CAAC;AACJ,CAAC"}