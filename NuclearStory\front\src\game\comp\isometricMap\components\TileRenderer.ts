import { WorldMap } from '../../../../shared/types/World'
import { TerrainType } from '../../../../shared/enums'
import { TileSize, getTileScreenPosition, isTileVisible } from '../utils/coordinates'

export interface TileRenderOptions {
  ctx: CanvasRenderingContext2D
  screenX: number
  screenY: number
  isoX: number
  isoY: number
  cameraX: number
  cameraY: number
  screenWidth: number
  screenHeight: number
  tileSize: TileSize
  currentWorld: WorldMap | null
  fogImageRef: React.MutableRefObject<HTMLImageElement | null>
}

/**
 * Получает цвет заливки тайла на основе его типа местности
 */
const getTerrainColor = (terrain?: TerrainType): string => {
  switch (terrain) {
    case TerrainType.WATER:
      return '#123652ff' // вода
    case TerrainType.GRASS:
      return '#1f5c28ff' // трава
    case TerrainType.DEADFOREST:
      return '#6E370F' // мёртвый лес
    case TerrainType.MOUNTAIN:
      return '#646464' // горы
    case TerrainType.DESERT:
      return '#daca7cff' // пустыня
    case TerrainType.SWAMP:
      return '#2a422aff' // болото
    case TerrainType.ROAD:
      return '#2c2b2bff' // дорога
    case TerrainType.CITY:
      return '#FFFFFF' // город
    case TerrainType.RUINS:
      return '#3f3730ff' // руины
    case TerrainType.WASTELAND:
      return '#615039ff' // пустошь
    default:
      return '#0000001a' // по умолчанию
  }
}

/**
 * Отрисовывает туман войны на тайле
 */
const drawFogOfWar = (
  ctx: CanvasRenderingContext2D,
  centerX: number,
  centerY: number,
  tileSize: TileSize,
  imgDirection: number,
  fogImage: HTMLImageElement
) => {
  ctx.save()
  ctx.beginPath()
  
  const halfTileW = tileSize.width / 2 - 0.2
  const halfTileH = tileSize.height / 2 - 0.2
  
  // Создаем клиппинг в форме ромба
  ctx.moveTo(centerX, centerY - halfTileH)
  ctx.lineTo(centerX + halfTileW, centerY)
  ctx.lineTo(centerX, centerY + halfTileH)
  ctx.lineTo(centerX - halfTileW, centerY)
  ctx.closePath()
  ctx.clip()

  // Поворот изображения в зависимости от imgDirection
  ctx.translate(centerX, centerY)
  let angle = 0
  if (imgDirection === 2) angle = Math.PI / 2
  if (imgDirection === 3) angle = Math.PI
  if (imgDirection === 4) angle = 3 * Math.PI / 2
  ctx.rotate(angle)

  // COVER: вычисляем коэффициент для полного покрытия ромба
  const imgRatio = fogImage.width / fogImage.height
  const tileRatio = tileSize.width / tileSize.height
  let drawW = tileSize.width
  let drawH = tileSize.height
  
  if (imgRatio > tileRatio) {
    // Изображение шире, чем ромб — увеличиваем высоту
    drawH = tileSize.width / imgRatio
    if (drawH < tileSize.height) drawH = tileSize.height
    drawW = drawH * imgRatio
  } else {
    // Изображение выше — увеличиваем ширину
    drawW = tileSize.height * imgRatio
    if (drawW < tileSize.width) drawW = tileSize.width
    drawH = drawW / imgRatio
  }

  ctx.drawImage(
    fogImage,
    -drawW / 2,
    -drawH / 2,
    drawW,
    drawH
  )

  ctx.restore()
}

/**
 * Отрисовывает ромбовидный тайл
 */
export const drawTile = (options: TileRenderOptions): void => {
  const {
    ctx,
    screenX,
    screenY,
    isoX,
    isoY,
    cameraX,
    cameraY,
    screenWidth,
    screenHeight,
    tileSize,
    currentWorld,
    fogImageRef
  } = options

  const { x: centerX, y: centerY } = getTileScreenPosition(
    screenX,
    screenY,
    cameraX,
    cameraY,
    screenWidth,
    screenHeight
  )

  // Проверяем, находится ли тайл в видимой области
  if (!isTileVisible(centerX, centerY, screenWidth, screenHeight, tileSize)) {
    return
  }

  const gap = -0.2 // размер отступа между тайлами
  const halfTileW = tileSize.width / 2 - gap
  const halfTileH = tileSize.height / 2 - gap

  // Получаем данные тайла из текущего мира
  const tileKey = `${isoX},${isoY}`
  const tileData = currentWorld?.worldMap?.[tileKey]

  // Рисуем ромб с отступом
  ctx.beginPath()
  ctx.moveTo(centerX, centerY - halfTileH) // Верх
  ctx.lineTo(centerX + halfTileW, centerY) // Право
  ctx.lineTo(centerX, centerY + halfTileH) // Низ
  ctx.lineTo(centerX - halfTileW, centerY) // Лево
  ctx.closePath()

  // Определяем цвет заливки на основе данных тайла
  let fillColor = getTerrainColor(tileData?.terrain)
  let strokeColor = '#444'

  // Подсветка игрока
  if (
    currentWorld?.player?.position &&
    isoX === currentWorld.player.position.x &&
    isoY === currentWorld.player.position.y
  ) {
    fillColor = '#c212b3ff' // игрок
  }

  if (tileData) {
    // Если тайл заблокирован, делаем его красноватым
    if (tileData.blocked) {
      fillColor = 'rgba(255, 0, 0, 0.2)'
      strokeColor = '#800'
    }

    // Если есть туман войны, отрисовываем его
    if (tileData?.fogOfWar && fogImageRef.current?.complete) {
      drawFogOfWar(
        ctx,
        centerX,
        centerY,
        tileSize,
        tileData.imgDirection || 1,
        fogImageRef.current
      )
    }
  }

  // Заливка
  ctx.fillStyle = fillColor
  ctx.fill()

  // Рисуем локацию, если есть
  if (tileData?.location && !tileData.fogOfWar) {
    ctx.fillStyle = '#FFD700'
    ctx.beginPath()
    ctx.arc(centerX, centerY - 5, 3, 0, 2 * Math.PI)
    ctx.fill()
  }
}

/**
 * Отрисовывает всю карту
 */
export const drawMap = (
  ctx: CanvasRenderingContext2D,
  screenWidth: number,
  screenHeight: number,
  cameraX: number,
  cameraY: number,
  tileSize: TileSize,
  currentWorld: WorldMap | null,
  fogImageRef: React.MutableRefObject<HTMLImageElement | null>,
  isoToScreen: (isoX: number, isoY: number) => { x: number; y: number }
): void => {
  // Очищаем канвас
  ctx.clearRect(0, 0, screenWidth, screenHeight)

  const mapSize = currentWorld?.settings?.worldSize || 20

  for (let isoY = 0; isoY < mapSize; isoY++) {
    for (let isoX = 0; isoX < mapSize; isoX++) {
      const { x: screenX, y: screenY } = isoToScreen(isoX, isoY)

      const { x: worldX, y: worldY } = getTileScreenPosition(
        screenX,
        screenY,
        cameraX,
        cameraY,
        screenWidth,
        screenHeight
      )

      if (
        worldX >= -tileSize.width &&
        worldX <= screenWidth + tileSize.width &&
        worldY >= -tileSize.height &&
        worldY <= screenHeight + tileSize.height
      ) {
        drawTile({
          ctx,
          screenX,
          screenY,
          isoX,
          isoY,
          cameraX,
          cameraY,
          screenWidth,
          screenHeight,
          tileSize,
          currentWorld,
          fogImageRef
        })
      }
    }
  }

  // Рисуем центральную точку для ориентации
  ctx.fillStyle = '#ff353500'
  ctx.beginPath()
  ctx.arc(screenWidth / 2, screenHeight / 2, 3, 0, 2 * Math.PI)
  ctx.fill()
}
