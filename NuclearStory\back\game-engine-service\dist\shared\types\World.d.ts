import { Location } from './Location';
import { Player, Position } from '../models/Player';
import { Language, TerrainType } from '../enums';
export interface WorldSettings {
    seed: string;
    language: Language;
    worldSize: number;
    difficulty: 'easy' | 'normal' | 'hard';
    autosave: 'everytime' | 'when_rest' | 'on_exit' | 'forbidden';
    timeScale: number;
}
export interface WorldSummary {
    id: string;
    name: string;
    description: string;
    createdAt: string;
    lastPlayedAt?: string;
    totalPlaytime?: number;
}
export interface WorldWeatherState {
    currentTime: {
        day: number;
        hour: number;
        minute: number;
        season: 'spring' | 'summer' | 'autumn' | 'winter';
    };
    weather: {
        temperature: number;
        humidity: number;
        windSpeed: number;
        precipitation: number;
        visibility: number;
        radiationStorm: boolean;
    };
    activeEvents: string[];
}
export interface WorldMap {
    id: string;
    userId: string;
    name: string;
    parameters: WorldWeatherState;
    description: string;
    settings: WorldSettings;
    worldMap: Record<string, WorldMapCell>;
    player?: Player;
    createdAt: Date;
    updatedAt: Date;
}
export interface WorldMapCell {
    pos: Position;
    blocked: boolean;
    terrarianMarker: number;
    terrain: TerrainType;
    height: number;
    location?: Location;
    fogOfWar: boolean;
    imgDirection: number;
    LVLZone: number;
}
