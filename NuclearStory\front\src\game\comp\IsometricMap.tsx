
import React, { useRef, useEffect, useState } from 'react'
import styles from './IsometricMap.module.css'
import { WorldMap } from '../../shared/types/World'
import { TerrainType } from '../../shared/enums'

interface IsometricMapProps {
  width?: number
  height?: number
  currentWorld: WorldMap | null
}

const IsometricMap: React.FC<IsometricMapProps> = ({
  width = 1920,
  height = 1080,
  currentWorld
}) => {
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const cameraRef = useRef({ x: 0, y: 0 })
  const fogImageRef = useRef<HTMLImageElement | null>(null)
  const [zoom, setZoom] = useState(1.0)
  const [cameraUI, setCameraUI] = useState({ x: 0, y: 0 })
  const wSize = currentWorld?.settings?.worldSize || 20




  // Константы для изометрической проекции
  const BASE_TILE_WIDTH = 80
  const BASE_TILE_HEIGHT = 40
  const MAP_SIZE = wSize




  // Масштабированные размеры тайлов, всегда чётные
  const TILE_WIDTH = Math.round(BASE_TILE_WIDTH * zoom / 2) * 2
  const TILE_HEIGHT = Math.round(BASE_TILE_HEIGHT * zoom / 2) * 2

  useEffect(() => {
    const img = new Image()
    img.src = '/textures/worldMap/fogOfWar/fogOfWar.webp'
    fogImageRef.current = img
  }, [])

  // выставляем камеру по центру карты
  useEffect(() => {
    // центр карты в тайлах (по изометрии)
    const mapSize = currentWorld?.settings?.worldSize || MAP_SIZE
    const centerTile = mapSize / 2

    // конвертим в экранные координаты центр карты
    const centerScreen = isoToScreen(centerTile, centerTile)

    // ставим камеру так, чтобы центр карты был в середине канваса
    cameraRef.current.x = centerScreen.x
    cameraRef.current.y = centerScreen.y

    draw()
  }, [currentWorld])


  // Циклическая отрисовка
  useEffect(() => {
    let animationFrameId: number
    let lastRenderTime = 0
    const targetFPS = 60
    const frameDuration = 1000 / targetFPS

    const renderLoop = (time: number) => {
      if (time - lastRenderTime >= frameDuration) {
        draw()
        lastRenderTime = time
      }
      animationFrameId = requestAnimationFrame(renderLoop)
    }

    animationFrameId = requestAnimationFrame(renderLoop)

    return () => cancelAnimationFrame(animationFrameId)
  }, [zoom])



  // Функция для отображения координат камеры в интерфейсе
  useEffect(() => {
    const interval = setInterval(() => {
      setCameraUI({
        x: Math.round(cameraRef.current.x),
        y: Math.round(cameraRef.current.y)
      })
    }, 10)

    return () => clearInterval(interval)
  }, [])




  // Функция преобразования изометрических координат в экранные
  const isoToScreen = (isoX: number, isoY: number) => {
    const screenX = (isoX - isoY) * (TILE_WIDTH / 2)
    const screenY = (isoX + isoY) * (TILE_HEIGHT / 2)
    return { x: screenX, y: screenY }
  }


  // Функция преобразования экранных координат в изометрические
  const screenToIso = (screenX: number, screenY: number) => {
    const isoX = (screenX / (TILE_WIDTH / 2) + screenY / (TILE_HEIGHT / 2)) / 2
    const isoY = (screenY / (TILE_HEIGHT / 2) - screenX / (TILE_WIDTH / 2)) / 2
    return { x: Math.floor(isoX), y: Math.floor(isoY) }
  }



  // Функция отрисовки ромбовидного тайла
  const drawTile = (ctx: CanvasRenderingContext2D, screenX: number, screenY: number, isoX: number, isoY: number) => {
    const centerX = screenX + width / 2 - cameraRef.current.x
    const centerY = screenY + height / 2 - cameraRef.current.y

    const gap = -0.2 // размер отступа между тайлами
    const halfTileW = TILE_WIDTH / 2 - gap
    const halfTileH = TILE_HEIGHT / 2 - gap




    // Проверяем, находится ли тайл в видимой области
    if (centerX < -TILE_WIDTH || centerX > width + TILE_WIDTH ||
      centerY < -TILE_HEIGHT || centerY > height + TILE_HEIGHT) {
      return
    }


    // Получаем данные тайла из текущего мира
    const tileKey = `${isoX},${isoY}`
    const tileData = currentWorld?.worldMap?.[tileKey]

    // Рисуем ромб с отступом
    ctx.beginPath()
    ctx.moveTo(centerX, centerY - halfTileH) // Верх
    ctx.lineTo(centerX + halfTileW, centerY) // Право
    ctx.lineTo(centerX, centerY + halfTileH) // Низ
    ctx.lineTo(centerX - halfTileW, centerY) // Лево
    ctx.closePath()

    // Определяем цвет заливки на основе данных тайла
    let fillColor = '#0000001a' // По умолчанию
    let strokeColor = '#444'


    // // 🎨 ОТЛАДКА подкраска LVLZone
    // if (tileData?.LVLZone === 1) fillColor = '#00ff8040';
    // if (tileData?.LVLZone === 2) fillColor = '#00aaff33';
    // if (tileData?.LVLZone === 3) fillColor = '#ffa5002e';
    // if (tileData?.LVLZone === 4) fillColor = '#ff3c3c26';

    // // 🧍‍♂️ ОТЛАДКА terrarianMarker
    //  if (tileData?.terrarianMarker === 1) fillColor = '#ff00ff66';

    // // 🧭 ОТЛАДКА imgDirection
    //  if (tileData?.imgDirection === 1) fillColor = '#ff000033';     // вверх
    //  if (tileData?.imgDirection === 2) fillColor = '#00ff0033';     // вниз
    //  if (tileData?.imgDirection === 3) fillColor = '#0000ff33';     // влево
    //  if (tileData?.imgDirection === 4) fillColor = '#ffff0033';   // вправо

    // // 🌄 ОТЛАДКА высоты (height)
    // if (tileData?.height === 0) fillColor = '#0064ff26';        // вода
    // if (tileData?.height === 1) fillColor = '#64ff6426';      // равнина
    // if (tileData?.height === 2) fillColor = '#a0d2642e';      // холм
    // if (tileData?.height === 3) fillColor = '#c8aa7833';       // возвышенность
    // if (tileData?.height === 4) fillColor = '#b4b4b438';      // гора

    // 🌍 ОТЛАДКА terrain (тип местности)
    if (tileData?.terrain === TerrainType.WATER) fillColor = '#123652ff'; // вода
    if (tileData?.terrain === TerrainType.GRASS) fillColor = '#1f5c28ff'; // трава
    if (tileData?.terrain === TerrainType.DEADFOREST) fillColor = '#6E370F';  // мёртвый лес
    if (tileData?.terrain === TerrainType.MOUNTAIN) fillColor = '#646464';  // горы
    if (tileData?.terrain === TerrainType.DESERT) fillColor = '#daca7cff'; // пустыня
    if (tileData?.terrain === TerrainType.SWAMP) fillColor = '#2a422aff'; // болото
    if (tileData?.terrain === TerrainType.ROAD) fillColor = '#2c2b2bff'; // дорога
    if (tileData?.terrain === TerrainType.CITY) fillColor = '#FFFFFF';  // город
    if (tileData?.terrain === TerrainType.RUINS) fillColor = '#3f3730ff'; // руины
    if (tileData?.terrain === TerrainType.WASTELAND) fillColor = '#615039ff'; // пустошь


    // 🌍 ОТЛАДКА игрока
    if (
      currentWorld?.player?.position && isoX === currentWorld.player.position.x && isoY === currentWorld.player.position.y) {fillColor = '#c212b3ff'; }// игрок

    if (tileData) {

      // Если тайл заблокирован, делаем его красноватым
      if (tileData.blocked) {
        fillColor = 'rgba(255, 0, 0, 0.2)'
        strokeColor = '#800'
      }

      // Если есть туман войны, затемняем

      if (tileData?.fogOfWar && fogImageRef.current?.complete) {
        ctx.save()
        ctx.beginPath()
        ctx.moveTo(centerX, centerY - halfTileH)
        ctx.lineTo(centerX + halfTileW, centerY)
        ctx.lineTo(centerX, centerY + halfTileH)
        ctx.lineTo(centerX - halfTileW, centerY)
        ctx.closePath()
        ctx.clip()

        // Поворот изображения в зависимости от imgDirection
        ctx.translate(centerX, centerY)
        let angle = 0
        if (tileData.imgDirection === 2) angle = Math.PI / 2
        if (tileData.imgDirection === 3) angle = Math.PI
        if (tileData.imgDirection === 4) angle = 3 * Math.PI / 2
        ctx.rotate(angle)

        // COVER: вычисляем коэффициент для полного покрытия ромба
        const img = fogImageRef.current
        const imgRatio = img.width / img.height
        const tileRatio = TILE_WIDTH / TILE_HEIGHT
        let drawW = TILE_WIDTH, drawH = TILE_HEIGHT
        if (imgRatio > tileRatio) {
          // Изображение шире, чем ромб — увеличиваем высоту
          drawH = TILE_WIDTH / imgRatio
          if (drawH < TILE_HEIGHT) drawH = TILE_HEIGHT
          drawW = drawH * imgRatio
        } else {
          // Изображение выше — увеличиваем ширину
          drawW = TILE_HEIGHT * imgRatio
          if (drawW < TILE_WIDTH) drawW = TILE_WIDTH
          drawH = drawW / imgRatio
        }

        ctx.drawImage(
          img,
          -drawW / 2,
          -drawH / 2,
          drawW,
          drawH
        )

        ctx.restore()
      }
    }

    // Заливка
    ctx.fillStyle = fillColor
    ctx.fill()


    // Рисуем локацию, если есть
    if (tileData?.location && !tileData.fogOfWar) {
      ctx.fillStyle = '#FFD700'
      ctx.beginPath()
      ctx.arc(centerX, centerY - 5, 3, 0, 2 * Math.PI)
      ctx.fill()
    }
  }

  const draw = () => {
    const canvas = canvasRef.current
    if (!canvas) return

    const ctx = canvas.getContext('2d')
    if (!ctx) return
    ctx.imageSmoothingEnabled = false // отключаем сглаживание

    // Очищаем канвас
    ctx.clearRect(0, 0, width, height)

    const mapSize = currentWorld?.settings?.worldSize || MAP_SIZE

    // Центр экрана
    const centerX = cameraRef.current.x
    const centerY = cameraRef.current.y


    for (let isoY = 0; isoY < mapSize; isoY++) {
      for (let isoX = 0; isoX < mapSize; isoX++) {
        const { x: screenX, y: screenY } = isoToScreen(isoX, isoY)

        const worldX = screenX + width / 2 - centerX
        const worldY = screenY + height / 2 - centerY

        if (
          worldX >= -TILE_WIDTH &&
          worldX <= width + TILE_WIDTH &&
          worldY >= -TILE_HEIGHT &&
          worldY <= height + TILE_HEIGHT
        ) {
          drawTile(ctx, screenX, screenY, isoX, isoY)
        }
      }
    }

    // Рисуем центральную точку для ориентации
    ctx.fillStyle = '#ff353500'
    ctx.beginPath()
    ctx.arc(width / 2, height / 2, 3, 0, 2 * Math.PI)
    ctx.fill()
  }


  // Обработчик движения мыши для перемещения камеры
  const handleMouseMove = (e: React.MouseEvent<HTMLCanvasElement>) => {
    // Перетаскивание только правой кнопкой мыши (e.buttons === 2)
    if (e.buttons === 2) {
      cameraRef.current.x -= e.movementX
      cameraRef.current.y -= e.movementY
    }
  }


  // Обработчик изменения зума
  const handleZoomChange = (newZoom: number) => {
    setZoom(Math.max(1, Math.min(2.5, newZoom)))
  }

  // Обработчик колеса мыши для зума
  const handleWheel = (e: WheelEvent) => {
    e.preventDefault()
    const zoomDelta = e.deltaY > 0 ? -0.1 : 0.1
    handleZoomChange(zoom + zoomDelta)
  }

  // Обработчик клика для получения координат
  const handleClick = (e: React.MouseEvent<HTMLCanvasElement>) => {
    const rect = canvasRef.current?.getBoundingClientRect()
    if (!rect) return


    // это
    const clickX = e.clientX - rect.left - width / 2 + cameraRef.current.x
    const clickY = e.clientY - rect.top - height / 2 + cameraRef.current.y


    const { x: isoX, y: isoY } = screenToIso(clickX, clickY)
    const mapSize = currentWorld?.settings?.worldSize || MAP_SIZE

    if (isoX >= 0 && isoX < mapSize && isoY >= 0 && isoY < mapSize) {

      // Получаем данные тайла
      const tileKey = `${isoX},${isoY}`
      const tileData = currentWorld?.worldMap?.[tileKey]


      // Здесь можно добавить логику обработки клика по тайлу
    }
  }

  // Обработчик клавиш для управления камерой
  const handleKeyDown = (e: KeyboardEvent) => {
    const moveSpeed = 10
    switch (e.key) {
      case 'ArrowUp':
        cameraRef.current.y -= moveSpeed
        break
      case 'ArrowDown':
        cameraRef.current.y += moveSpeed
        break
      case 'ArrowLeft':
        cameraRef.current.x -= moveSpeed
        break
      case 'ArrowRight':
        cameraRef.current.x += moveSpeed
        break
      default:
        return
    }
    e.preventDefault()
  }


  // Эффект для добавления обработчика клавиш
  useEffect(() => {
    window.addEventListener('keydown', handleKeyDown)
    return () => {
      window.removeEventListener('keydown', handleKeyDown)
    }
  }, [])

  // Начальная отрисовка
  useEffect(() => {
    draw()
  }, [])

  // Подключаем wheel через addEventListener с passive: false
  useEffect(() => {
    const canvas = canvasRef.current
    if (!canvas) return
    const wheelHandler = (e: WheelEvent) => handleWheel(e)
    canvas.addEventListener('wheel', wheelHandler, { passive: false })
    return () => canvas.removeEventListener('wheel', wheelHandler)
  }, [zoom])

  // Отключаем контекстное меню (правый клик) на canvas
  useEffect(() => {
    const canvas = canvasRef.current
    if (!canvas) return
    const handler = (e: MouseEvent) => e.preventDefault()
    canvas.addEventListener('contextmenu', handler)
    return () => canvas.removeEventListener('contextmenu', handler)
  }, [])

  return (
    <div className={styles.mapContainer}>
      <canvas
        ref={canvasRef}
        width={width}
        height={height}
        className={styles.mapCanvas}
        onMouseMove={handleMouseMove}
        onClick={handleClick}
        style={{ cursor: 'grab' }}
      />
      <div className={styles.mapControls}>
        <div className={styles.coordinates}>
          Камера:  ({cameraUI.x}, {cameraUI.y})
        </div>

        <div className={styles.instructions}>
          ПКМ - перетаскивание, стрелки - движение, колесо - зум
        </div>
      </div>
      <div className={styles.zoomControls}>
        <label className={styles.zoomLabel}>
          Зум: {zoom.toFixed(1)}x
        </label>
        <input
          type="range"
          min="1"
          max="2.5"
          step="0.1"
          value={zoom}
          onChange={(e) => handleZoomChange(parseFloat(e.target.value))}
          className={styles.zoomSlider}
        />
      </div>
    </div>
  )
}

export default IsometricMap
