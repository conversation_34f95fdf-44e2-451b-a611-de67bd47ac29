"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.generateBaseWorld = generateBaseWorld;
const uuid_1 = require("uuid");
const enums_1 = require("../shared/enums");
function generateBaseWorld(createWorldDto) {
    const worldId = (0, uuid_1.v4)();
    const currentDate = new Date();
    const weatherState = generateWorldWeatherState();
    const worldSize = createWorldDto.settings.worldSize;
    const centerPos = {
        x: Math.floor(worldSize / 2),
        y: Math.floor(worldSize / 2)
    };
    const initialPlayer = generateInitialPlayer(createWorldDto.userId, createWorldDto.name || 'Player', centerPos);
    const world = {
        id: worldId,
        userId: createWorldDto.userId,
        name: createWorldDto.name,
        description: createWorldDto.description,
        parameters: weatherState,
        player: initialPlayer,
        settings: {
            seed: createWorldDto.settings.seed,
            language: createWorldDto.settings.language,
            worldSize: createWorldDto.settings.worldSize,
            difficulty: createWorldDto.settings.difficulty,
            autosave: createWorldDto.settings.autosave,
            timeScale: createWorldDto.settings.timeScale,
        },
        worldMap: {},
        createdAt: currentDate,
        updatedAt: currentDate,
    };
    world.worldMap = generateWorldGrid(world.settings, world.settings.seed);
    return world;
}
function generateInitialPlayer(userId, name, position) {
    const now = new Date();
    return {
        id: (0, uuid_1.v4)(),
        name,
        level: 1,
        experience: 0,
        experienceToNext: 100,
        imgDirection: 1,
        special: { S: 5, P: 5, E: 5, C: 5, I: 5, A: 5, L: 5 },
        currentHP: 100,
        maxHP: 100,
        currentAP: 50,
        maxAP: 50,
        radiationLevel: 0,
        hunger: 0,
        thirst: 0,
        fatigue: 0,
        perks: [],
        skills: {},
        inventory: [],
        position: position,
        effects: [],
        completedQuests: [],
        activeQuests: [],
        discoveredLocations: [],
        factionReputation: {},
        createdAt: now,
        lastSaveAt: now,
    };
}
function generateWorldWeatherState() {
    return {
        currentTime: {
            day: 1,
            hour: 12,
            minute: 0,
            season: 'spring',
        },
        weather: {
            temperature: 20,
            humidity: 65,
            windSpeed: 5,
            precipitation: 0,
            visibility: 100,
            radiationStorm: false,
        },
        activeEvents: [],
    };
}
function generateWorldGrid(settings, seed) {
    const grid = {};
    const worldSize = settings.worldSize;
    const rng = createSeededRandom(seed);
    const locationChance = 0;
    for (let x = 0; x < worldSize; x++) {
        for (let y = 0; y < worldSize; y++) {
            const pos = { x, y };
            const cellKey = `${x},${y}`;
            const terrain = enums_1.TerrainType.WASTELAND;
            const blocked = false;
            let height = 1;
            if (x === 0 || y === 0 || x === worldSize - 1 || y === worldSize - 1)
                height = 0;
            else if ((x + y) % 7 === 0)
                height = 2;
            else if ((x * y) % 13 === 0)
                height = 3;
            else if ((x + y) % 17 === 0)
                height = 4;
            let location = undefined;
            grid[cellKey] = {
                pos,
                blocked,
                terrarianMarker: 0,
                terrain,
                height,
                location,
                fogOfWar: true,
                imgDirection: 1,
                LVLZone: 1,
            };
        }
    }
    const centerX = Math.floor(worldSize / 2);
    const centerY = Math.floor(worldSize / 2);
    openFogOfWar(grid, { x: centerX, y: centerY }, 7);
    function openFogOfWar(grid, pos, radius) {
        for (let x = pos.x - radius; x <= pos.x + radius; x++) {
            for (let y = pos.y - radius; y <= pos.y + radius; y++) {
                const dx = x - pos.x;
                const dy = y - pos.y;
                if (dx * dx + dy * dy <= radius * radius) {
                    const cellKey = `${x},${y}`;
                    if (grid[cellKey]) {
                        grid[cellKey].fogOfWar = false;
                    }
                }
            }
        }
    }
    for (let x = 0; x < worldSize; x++) {
        for (let y = 0; y < worldSize; y++) {
            const cellKey = `${x},${y}`;
            grid[cellKey].terrarianMarker = Math.random() < 0.05 ? 1 : 0;
            if (grid[cellKey].terrarianMarker === 1) {
                x + 2;
                y + 2;
            }
        }
    }
    const center = worldSize / 2;
    for (let x = 0; x < worldSize; x++) {
        for (let y = 0; y < worldSize; y++) {
            const cellKey = `${x},${y}`;
            const dist = Math.sqrt((x - center) ** 2 + (y - center) ** 2);
            let LVLZone = 1;
            if (dist > worldSize * 0.4)
                LVLZone = 4;
            else if (dist > worldSize * 0.27)
                LVLZone = 3;
            else if (dist > worldSize * 0.13)
                LVLZone = 2;
            grid[cellKey].LVLZone = LVLZone;
        }
    }
    for (let x = 0; x < worldSize; x++) {
        for (let y = 0; y < worldSize; y++) {
            const cellKey = `${x},${y}`;
            const imgDirection = (Math.floor(Math.random() * 4) + 1);
            grid[cellKey].imgDirection = imgDirection;
        }
    }
    return grid;
}
function createSeededRandom(seed) {
    let hash = 0;
    for (let i = 0; i < seed.length; i++) {
        hash = (hash << 5) - hash + seed.charCodeAt(i);
        hash |= 0;
    }
    return function () {
        hash = (hash * 9301 + 49297) % 233280;
        return hash / 233280;
    };
}
//# sourceMappingURL=worldGenerator.js.map