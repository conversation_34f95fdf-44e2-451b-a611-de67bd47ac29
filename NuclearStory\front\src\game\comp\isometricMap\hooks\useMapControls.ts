import { useRef, useEffect, useState, useCallback } from 'react'
import { WorldMap } from '../../../../shared/types/World'
import { screenToIso, TileSize } from '../utils/coordinates'

export interface Camera {
  x: number
  y: number
}

export interface MapControlsOptions {
  width: number
  height: number
  currentWorld: WorldMap | null
  tileSize: TileSize
  isoToScreen: (isoX: number, isoY: number) => { x: number; y: number }
}

export interface MapControlsReturn {
  cameraRef: React.MutableRefObject<Camera>
  zoom: number
  cameraUI: Camera
  handleMouseMove: (e: React.MouseEvent<HTMLCanvasElement>) => void
  handleClick: (e: React.MouseEvent<HTMLCanvasElement>) => void
  handleZoomChange: (newZoom: number) => void
  setupEventListeners: (canvas: HTMLCanvasElement) => () => void
}

export const useMapControls = (options: MapControlsOptions): MapControlsReturn => {
  const { width, height, currentWorld, tileSize, isoToScreen } = options
  
  const cameraRef = useRef<Camera>({ x: 0, y: 0 })
  const [zoom, setZoom] = useState(1.0)
  const [cameraUI, setCameraUI] = useState<Camera>({ x: 0, y: 0 })

  // Обработчик движения мыши для перемещения камеры
  const handleMouseMove = useCallback((e: React.MouseEvent<HTMLCanvasElement>) => {
    // Перетаскивание только правой кнопкой мыши (e.buttons === 2)
    if (e.buttons === 2) {
      cameraRef.current.x -= e.movementX
      cameraRef.current.y -= e.movementY
    }
  }, [])

  // Обработчик изменения зума
  const handleZoomChange = useCallback((newZoom: number) => {
    setZoom(Math.max(1, Math.min(2.5, newZoom)))
  }, [])

  // Обработчик колеса мыши для зума
  const handleWheel = useCallback((e: WheelEvent) => {
    e.preventDefault()
    const zoomDelta = e.deltaY > 0 ? -0.1 : 0.1
    handleZoomChange(zoom + zoomDelta)
  }, [zoom, handleZoomChange])

  // Обработчик клика для получения координат
  const handleClick = useCallback((e: React.MouseEvent<HTMLCanvasElement>) => {
    const rect = e.currentTarget.getBoundingClientRect()
    if (!rect) return

    const clickX = e.clientX - rect.left - width / 2 + cameraRef.current.x
    const clickY = e.clientY - rect.top - height / 2 + cameraRef.current.y

    const { x: isoX, y: isoY } = screenToIso(clickX, clickY, tileSize)
    const mapSize = currentWorld?.settings?.worldSize || 20

    if (isoX >= 0 && isoX < mapSize && isoY >= 0 && isoY < mapSize) {
      // Получаем данные тайла
      const tileKey = `${isoX},${isoY}`
      const tileData = currentWorld?.worldMap?.[tileKey]

      // Здесь можно добавить логику обработки клика по тайлу
      console.log('Clicked tile:', { isoX, isoY, tileData })
    }
  }, [width, height, currentWorld, tileSize])

  // Обработчик клавиш для управления камерой
  const handleKeyDown = useCallback((e: KeyboardEvent) => {
    const moveSpeed = 10
    switch (e.key) {
      case 'ArrowUp':
        cameraRef.current.y -= moveSpeed
        break
      case 'ArrowDown':
        cameraRef.current.y += moveSpeed
        break
      case 'ArrowLeft':
        cameraRef.current.x -= moveSpeed
        break
      case 'ArrowRight':
        cameraRef.current.x += moveSpeed
        break
      default:
        return
    }
    e.preventDefault()
  }, [])

  // Функция для настройки обработчиков событий на canvas
  const setupEventListeners = useCallback((canvas: HTMLCanvasElement) => {
    const wheelHandler = (e: WheelEvent) => handleWheel(e)
    const contextMenuHandler = (e: MouseEvent) => e.preventDefault()

    canvas.addEventListener('wheel', wheelHandler, { passive: false })
    canvas.addEventListener('contextmenu', contextMenuHandler)

    return () => {
      canvas.removeEventListener('wheel', wheelHandler)
      canvas.removeEventListener('contextmenu', contextMenuHandler)
    }
  }, [handleWheel])

  // Выставляем камеру по центру карты при изменении мира
  useEffect(() => {
    if (!currentWorld) return

    const mapSize = currentWorld.settings?.worldSize || 20
    const centerTile = mapSize / 2

    // Конвертим в экранные координаты центр карты
    const centerScreen = isoToScreen(centerTile, centerTile)

    // Ставим камеру так, чтобы центр карты был в середине канваса
    cameraRef.current.x = centerScreen.x
    cameraRef.current.y = centerScreen.y
  }, [currentWorld, isoToScreen])

  // Обновление UI координат камеры
  useEffect(() => {
    const interval = setInterval(() => {
      setCameraUI({
        x: Math.round(cameraRef.current.x),
        y: Math.round(cameraRef.current.y)
      })
    }, 10)

    return () => clearInterval(interval)
  }, [])

  // Эффект для добавления обработчика клавиш
  useEffect(() => {
    window.addEventListener('keydown', handleKeyDown)
    return () => {
      window.removeEventListener('keydown', handleKeyDown)
    }
  }, [handleKeyDown])

  return {
    cameraRef,
    zoom,
    cameraUI,
    handleMouseMove,
    handleClick,
    handleZoomChange,
    setupEventListeners
  }
}
