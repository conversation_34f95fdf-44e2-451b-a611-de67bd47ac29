import React from 'react'
import styles from '../styles/MapControls.module.css'
import { Camera } from '../hooks/useMapControls'

export interface MapControlsProps {
  cameraUI: Camera
}

const MapControls: React.FC<MapControlsProps> = ({ cameraUI }) => {
  return (
    <div className={styles.mapControls}>
      <div className={styles.coordinates}>
        Камера: ({cameraUI.x}, {cameraUI.y})
      </div>
      <div className={styles.instructions}>
        ПКМ - перетаскивание, стрелки - движение, колесо - зум
      </div>
    </div>
  )
}

export default MapControls
