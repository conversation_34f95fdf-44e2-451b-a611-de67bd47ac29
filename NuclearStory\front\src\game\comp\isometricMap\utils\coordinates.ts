// Константы для изометрической проекции
export const BASE_TILE_WIDTH = 80
export const BASE_TILE_HEIGHT = 40

// Интерфейс для координат
export interface Coordinates {
  x: number
  y: number
}

// Интерфейс для размеров тайла
export interface TileSize {
  width: number
  height: number
}

/**
 * Вычисляет размеры тайла с учетом зума
 * @param zoom - коэффициент масштабирования
 * @returns размеры тайла (всегда четные числа)
 */
export const getTileSize = (zoom: number): TileSize => {
  // Масштабированные размеры тайлов, всегда чётные
  const width = Math.round(BASE_TILE_WIDTH * zoom / 2) * 2
  const height = Math.round(BASE_TILE_HEIGHT * zoom / 2) * 2
  
  return { width, height }
}

/**
 * Функция преобразования изометрических координат в экранные
 * @param isoX - изометрическая координата X
 * @param isoY - изометрическая координата Y
 * @param tileSize - размеры тайла
 * @returns экранные координаты
 */
export const isoToScreen = (isoX: number, isoY: number, tileSize: TileSize): Coordinates => {
  const screenX = (isoX - isoY) * (tileSize.width / 2)
  const screenY = (isoX + isoY) * (tileSize.height / 2)
  return { x: screenX, y: screenY }
}

/**
 * Функция преобразования экранных координат в изометрические
 * @param screenX - экранная координата X
 * @param screenY - экранная координата Y
 * @param tileSize - размеры тайла
 * @returns изометрические координаты (округленные до целых)
 */
export const screenToIso = (screenX: number, screenY: number, tileSize: TileSize): Coordinates => {
  const isoX = (screenX / (tileSize.width / 2) + screenY / (tileSize.height / 2)) / 2
  const isoY = (screenY / (tileSize.height / 2) - screenX / (tileSize.width / 2)) / 2
  return { x: Math.floor(isoX), y: Math.floor(isoY) }
}

/**
 * Проверяет, находится ли тайл в видимой области экрана
 * @param centerX - центр тайла по X
 * @param centerY - центр тайла по Y
 * @param screenWidth - ширина экрана
 * @param screenHeight - высота экрана
 * @param tileSize - размеры тайла
 * @returns true, если тайл видим
 */
export const isTileVisible = (
  centerX: number,
  centerY: number,
  screenWidth: number,
  screenHeight: number,
  tileSize: TileSize
): boolean => {
  return !(
    centerX < -tileSize.width ||
    centerX > screenWidth + tileSize.width ||
    centerY < -tileSize.height ||
    centerY > screenHeight + tileSize.height
  )
}

/**
 * Вычисляет экранные координаты тайла с учетом камеры
 * @param screenX - базовая экранная координата X
 * @param screenY - базовая экранная координата Y
 * @param cameraX - позиция камеры по X
 * @param cameraY - позиция камеры по Y
 * @param screenWidth - ширина экрана
 * @param screenHeight - высота экрана
 * @returns координаты тайла на экране
 */
export const getTileScreenPosition = (
  screenX: number,
  screenY: number,
  cameraX: number,
  cameraY: number,
  screenWidth: number,
  screenHeight: number
): Coordinates => {
  const centerX = screenX + screenWidth / 2 - cameraX
  const centerY = screenY + screenHeight / 2 - cameraY
  return { x: centerX, y: centerY }
}
