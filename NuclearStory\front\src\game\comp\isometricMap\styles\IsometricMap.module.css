.mapContainer {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
  border-radius: 8px;
  overflow: hidden;
}

.mapCanvas {
  cursor: grab;
  border-radius: 4px;
  background: var(--bg-primary);
  box-shadow: inset 0 0 20px rgba(0, 0, 0, 0.5);
  user-select: none;
  -webkit-user-drag: none;
}

.mapCanvas:active {
  cursor: grabbing !important;
}
