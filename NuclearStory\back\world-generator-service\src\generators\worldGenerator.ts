import { v4 as uuidv4 } from 'uuid';
import { WorldMap, WorldSettings, WorldMapCell, WorldWeatherState } from '../shared/types/World';
import { Player } from '../shared/models/Player';
import { Position } from '../shared/models/Player';
import { TerrainType, Language } from '../shared/enums';
import { CreateWorldDto } from '../dto/create-world.dto';

// Генератор основного объекта мира
export function generateBaseWorld(createWorldDto: CreateWorldDto): WorldMap {
  const worldId = uuidv4();
  const currentDate = new Date();

  // Генерация погоды и состояния мира
  const weatherState: WorldWeatherState = generateWorldWeatherState();

  // Генерация стартового игрока
  const worldSize = createWorldDto.settings.worldSize;
  const centerPos: Position = {
    x: Math.floor(worldSize / 2),
    y: Math.floor(worldSize / 2)
  };
  const initialPlayer: Player = generateInitialPlayer(
    createWorldDto.userId,
    createWorldDto.name || 'Player',
    centerPos
  );

  // Создание базового объекта мира
  const world: WorldMap = {
    id: worldId,
    userId: createWorldDto.userId,
    name: createWorldDto.name,
    description: createWorldDto.description,
    parameters: weatherState,
    player: initialPlayer,
    settings: {
      seed: createWorldDto.settings.seed,
      language: createWorldDto.settings.language as Language, // Преобразуем в перечисление Language
      worldSize: createWorldDto.settings.worldSize,
      difficulty: createWorldDto.settings.difficulty,
      autosave: createWorldDto.settings.autosave,
      timeScale: createWorldDto.settings.timeScale,
    },
    worldMap: {}, // Пустая карта, которую заполним ниже
    createdAt: currentDate,
    updatedAt: currentDate,
  };

  // Генерация карты мира (сетки)
  world.worldMap = generateWorldGrid(world.settings, world.settings.seed);

  return world;
}
// Генератор стартового игрока
function generateInitialPlayer(userId: string, name: string, position: Position): Player {
  const now = new Date();
  return {
    id: uuidv4(),
    name,
    level: 1,
    experience: 0,
    experienceToNext: 100,
    imgDirection: 1,
    special: { S: 5, P: 5, E: 5, C: 5, I: 5, A: 5, L: 5 },
    currentHP: 100,
    maxHP: 100,
    currentAP: 50,
    maxAP: 50,
    radiationLevel: 0,
    hunger: 0,
    thirst: 0,
    fatigue: 0,
    perks: [],
    skills: {},
    inventory: [],
    position: position, // Явно указываем позицию спавна
    effects: [],
    completedQuests: [],
    activeQuests: [],
    discoveredLocations: [],
    factionReputation: {},
    createdAt: now,
    lastSaveAt: now,
  };
}

// Генератор состояния погоды и времени в мире
function generateWorldWeatherState(): WorldWeatherState {
  return {
    currentTime: {
      day: 1,
      hour: 12,
      minute: 0,
      season: 'spring', // Начинаем с весны
    },
    weather: {
      temperature: 20, // Приятная начальная температура
      humidity: 65,
      windSpeed: 5,
      precipitation: 0, // Без осадков в начале
      visibility: 100, // Хорошая видимость
      radiationStorm: false, // Без радиационного шторма в начале
    },
    activeEvents: [], // Без активных событий в начале
  };
}

// Генератор сетки карты мира
function generateWorldGrid(settings: WorldSettings, seed: string): Record<string, WorldMapCell> {
  const grid: Record<string, WorldMapCell> = {};
  const worldSize = settings.worldSize;

  // Используем сид для обеспечения детерминированной генерации
  const rng = createSeededRandom(seed);

  // Концентрация локаций - пока установлена на 0
  const locationChance = 0; // 0% шанс появления локации

  // Первый проход: базовые параметры (без terrarianMarker)
  for (let x = 0; x < worldSize; x++) {
    for (let y = 0; y < worldSize; y++) {
      const pos: Position = { x, y };
      const cellKey = `${x},${y}`;
      const terrain = TerrainType.WASTELAND;
      const blocked = false;
      let height = 1;
      if (x === 0 || y === 0 || x === worldSize - 1 || y === worldSize - 1) height = 0;
      else if ((x + y) % 7 === 0) height = 2;
      else if ((x * y) % 13 === 0) height = 3;
      else if ((x + y) % 17 === 0) height = 4;
      let location = undefined;
      grid[cellKey] = {
        pos,
        blocked,
        terrarianMarker: 0,
        terrain,
        height,
        location,
        fogOfWar: true,
        imgDirection: 1,
        LVLZone: 1,
      };
    }
  }

  // Открываем туман войны вокруг центра (позиция игрока)
  const centerX = Math.floor(worldSize / 2);
  const centerY = Math.floor(worldSize / 2);
  openFogOfWar(grid, { x: centerX, y: centerY }, 7);
// Открывает туман войны в радиусе radius от позиции pos
function openFogOfWar(grid: Record<string, WorldMapCell>, pos: Position, radius: number) {
  for (let x = pos.x - radius; x <= pos.x + radius; x++) {
    for (let y = pos.y - radius; y <= pos.y + radius; y++) {
      const dx = x - pos.x;
      const dy = y - pos.y;
      if (dx * dx + dy * dy <= radius * radius) {
        const cellKey = `${x},${y}`;
        if (grid[cellKey]) {
          grid[cellKey].fogOfWar = false;
        }
      }
    }
  }
}

  // Второй проход: terrarianMarker
  for (let x = 0; x < worldSize; x++) {
    for (let y = 0; y < worldSize; y++) {
      const cellKey = `${x},${y}`;
      grid[cellKey].terrarianMarker = Math.random() < 0.05 ? 1 : 0;
      if (grid[cellKey].terrarianMarker === 1) {
        x + 2;
        y + 2; // Пропускаем следующую итерацию
      }
    }
  }

  // Второй проход: LVLZone
  const center = worldSize / 2;
  for (let x = 0; x < worldSize; x++) {
    for (let y = 0; y < worldSize; y++) {
      const cellKey = `${x},${y}`;
      const dist = Math.sqrt((x - center) ** 2 + (y - center) ** 2);
      let LVLZone: 1 | 2 | 3 | 4 = 1;
      if (dist > worldSize * 0.4) LVLZone = 4;
      else if (dist > worldSize * 0.27) LVLZone = 3;
      else if (dist > worldSize * 0.13) LVLZone = 2;
      grid[cellKey].LVLZone = LVLZone;
    }
  }

  // Третий проход: imgDirection
  for (let x = 0; x < worldSize; x++) {
    for (let y = 0; y < worldSize; y++) {
      const cellKey = `${x},${y}`;
      const imgDirection = (Math.floor(Math.random() * 4) + 1) as 1 | 2 | 3 | 4;
      grid[cellKey].imgDirection = imgDirection;
    }
  }

  return grid;
}

// Утилиты для генерации

// Создает псевдо-случайный генератор чисел на основе сида
function createSeededRandom(seed: string) {
  let hash = 0;
  for (let i = 0; i < seed.length; i++) {
    hash = (hash << 5) - hash + seed.charCodeAt(i);
    hash |= 0; // Convert to 32bit integer
  }

  // Простая реализация PRNG
  return function () {
    hash = (hash * 9301 + 49297) % 233280;
    return hash / 233280;
  };
}
