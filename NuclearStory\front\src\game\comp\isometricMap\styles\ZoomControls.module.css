.zoomControls {
  position: absolute;
  top: 96px;
  left: 10px;
  background: var(--bg-overlay);
  padding: 8px 12px;
  border-radius: 6px;
  border: 1px solid var(--border-color);
  backdrop-filter: blur(5px);
  display: flex;
  flex-direction: column;
  gap: 8px;
  min-width: 150px;
}

.zoomLabel {
  color: var(--text-primary);
  font-size: 12px;
  font-family: monospace;
}

.zoomSlider {
  width: 100%;
  height: 4px;
  border-radius: 2px;
  background: var(--border-color);
  outline: none;
  -webkit-appearance: none;
  appearance: none;
}

.zoomSlider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background: var(--primary-color);
  cursor: pointer;
  box-shadow: 0 0 8px var(--shadow-glow);
  transition: all 0.3s ease;
}

.zoomSlider::-webkit-slider-thumb:hover {
  background: var(--primary-dark);
  transform: scale(1.1);
}

.zoomSlider::-moz-range-thumb {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background: var(--primary-color);
  cursor: pointer;
  border: none;
  box-shadow: 0 0 8px var(--shadow-glow);
  transition: all 0.3s ease;
}

.zoomSlider::-moz-range-thumb:hover {
  background: var(--primary-dark);
  transform: scale(1.1);
}
