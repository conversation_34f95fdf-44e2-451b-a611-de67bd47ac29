import React from 'react'
import styles from '../styles/ZoomControls.module.css'

export interface ZoomControlsProps {
  zoom: number
  onZoomChange: (newZoom: number) => void
}

const ZoomControls: React.FC<ZoomControlsProps> = ({ zoom, onZoomChange }) => {
  return (
    <div className={styles.zoomControls}>
      <label className={styles.zoomLabel}>
        Зум: {zoom.toFixed(1)}x
      </label>
      <input
        type="range"
        min="1"
        max="2.5"
        step="0.1"
        value={zoom}
        onChange={(e) => onZoomChange(parseFloat(e.target.value))}
        className={styles.zoomSlider}
      />
    </div>
  )
}

export default ZoomControls
