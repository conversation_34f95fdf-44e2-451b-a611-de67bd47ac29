import React, { useRef, useEffect } from 'react'
import styles from './styles/IsometricMap.module.css'
import { WorldMap } from '../../../shared/types/World'
import { MapControls, ZoomControls, drawMap } from './components'
import { useMapControls } from './hooks'
import { getTileSize, isoToScreen } from './utils'

interface IsometricMapProps {
  width?: number
  height?: number
  currentWorld: WorldMap | null
}

const IsometricMap: React.FC<IsometricMapProps> = ({
  width = 1920,
  height = 1080,
  currentWorld
}) => {
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const fogImageRef = useRef<HTMLImageElement | null>(null)

  // Вычисляем размеры тайлов на основе зума
  const tileSize = getTileSize(1.0) // Базовый размер для инициализации

  // Создаем функцию преобразования координат с текущими размерами тайлов
  const isoToScreenWithSize = (isoX: number, isoY: number) => 
    isoToScreen(isoX, isoY, getTileSize(zoom))

  // Используем хук для управления картой
  const {
    cameraRef,
    zoom,
    cameraUI,
    handleMouseMove,
    handleClick,
    handleZoomChange,
    setupEventListeners
  } = useMapControls({
    width,
    height,
    currentWorld,
    tileSize: getTileSize(zoom),
    isoToScreen: isoToScreenWithSize
  })

  // Загружаем изображение тумана войны
  useEffect(() => {
    const img = new Image()
    img.src = '/textures/worldMap/fogOfWar/fogOfWar.webp'
    fogImageRef.current = img
  }, [])

  // Функция отрисовки
  const draw = () => {
    const canvas = canvasRef.current
    if (!canvas) return

    const ctx = canvas.getContext('2d')
    if (!ctx) return
    
    ctx.imageSmoothingEnabled = false // отключаем сглаживание

    const currentTileSize = getTileSize(zoom)
    
    drawMap(
      ctx,
      width,
      height,
      cameraRef.current.x,
      cameraRef.current.y,
      currentTileSize,
      currentWorld,
      fogImageRef,
      (isoX, isoY) => isoToScreen(isoX, isoY, currentTileSize)
    )
  }

  // Циклическая отрисовка
  useEffect(() => {
    let animationFrameId: number
    let lastRenderTime = 0
    const targetFPS = 60
    const frameDuration = 1000 / targetFPS

    const renderLoop = (time: number) => {
      if (time - lastRenderTime >= frameDuration) {
        draw()
        lastRenderTime = time
      }
      animationFrameId = requestAnimationFrame(renderLoop)
    }

    animationFrameId = requestAnimationFrame(renderLoop)

    return () => cancelAnimationFrame(animationFrameId)
  }, [zoom, currentWorld])

  // Начальная отрисовка
  useEffect(() => {
    draw()
  }, [])

  // Настройка обработчиков событий для canvas
  useEffect(() => {
    const canvas = canvasRef.current
    if (!canvas) return
    
    return setupEventListeners(canvas)
  }, [setupEventListeners])

  return (
    <div className={styles.mapContainer}>
      <canvas
        ref={canvasRef}
        width={width}
        height={height}
        className={styles.mapCanvas}
        onMouseMove={handleMouseMove}
        onClick={handleClick}
        style={{ cursor: 'grab' }}
      />
      
      <MapControls cameraUI={cameraUI} />
      
      <ZoomControls 
        zoom={zoom} 
        onZoomChange={handleZoomChange} 
      />
    </div>
  )
}

export default IsometricMap
